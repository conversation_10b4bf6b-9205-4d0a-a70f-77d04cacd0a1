/**
 * WCAG Rule 23: Accessible Authentication (Enhanced) - 3.3.9
 * 40% Automated - Requires extensive manual review for enhanced authentication requirements
 */

import { Page } from 'puppeteer';

import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import { AdvancedPatternDetector } from '../utils/advanced-pattern-detector';
import { PatternRecognitionEngine } from '../utils/pattern-recognition-engine';

export interface EnhancedAuthenticationElement {
  selector: string;
  type: 'cognitive-test' | 'memory-test' | 'recognition-test' | 'transcription-test' | 'other';
  cognitiveLoad: 'none' | 'low' | 'medium' | 'high';
  hasObjectRecognition: boolean;
  hasTextTranscription: boolean;
  hasMemoryRequirement: boolean;
  hasAlternative: boolean;
  alternativeTypes: string[];
  description: string;
  severity: 'info' | 'warning' | 'error';
}

export interface CognitiveAssessment {
  overallCognitiveLoad: 'none' | 'low' | 'medium' | 'high';
  cognitiveTests: EnhancedAuthenticationElement[];
  alternatives: string[];
  compliance: 'pass' | 'fail' | 'needs-review';
  recommendations: string[];
}

export interface AccessibleAuthenticationEnhancedConfig extends EnhancedCheckConfig {
  enableEnhancedCognitiveLoadAssessment?: boolean;
  enableAAALevelAuthenticationValidation?: boolean;
  enableBiometricAlternativeAnalysis?: boolean;
  enableEnhancedAccessibilityTesting?: boolean;
  enableFormAccessibilityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAISemanticValidation?: boolean;
}

export class AccessibleAuthenticationEnhancedCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private checkTemplate = new ManualReviewTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  /**
   * Perform enhanced accessible authentication check - 40% automated with enhanced evidence
   */
  async performCheck(
    config: AccessibleAuthenticationEnhancedConfig,
  ): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration (used for evidence standardization)

    const manualReviewConfig: ManualReviewConfig = {
      ...config,
      enableManualTracking: true,
      maxManualItems: 10,
    };

    const result = await this.checkTemplate.executeManualReviewCheck(
      'WCAG-037',
      'Accessible Authentication (Enhanced)',
      'understandable',
      0.03,
      'AAA',
      0.4, // 40% automation rate
      manualReviewConfig,
      this.executeEnhancedAuthenticationCheck.bind(this),
    );

    // Enhanced evidence standardization with cognitive assessment analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-037',
        ruleName: 'Accessible Authentication (Enhanced)',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.4,
          checkType: 'cognitive-assessment-analysis',
          manualReviewRequired: (result.manualReviewItems?.length ?? 0) > 0,
          cognitiveTestDetection: true,
          enhancedAlternativeValidation: true,
          comprehensiveCognitiveAssessment: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 20,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive enhanced authentication analysis
   */
  private async executeEnhancedAuthenticationCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Detect enhanced authentication elements
      const enhancedElements = await this.detectEnhancedAuthenticationElements(page);

      // Perform cognitive assessment
      const cognitiveAssessment = await this.performCognitiveAssessment(page, enhancedElements);

      // Check for object recognition requirements
      const objectRecognition = await this.checkObjectRecognition(page);

      // Check for text transcription requirements
      const textTranscription = await this.checkTextTranscription(page);

      // Evaluate enhanced alternatives
      const enhancedAlternatives = await this.evaluateEnhancedAlternatives(page);

      let totalChecks = 0;
      let passedChecks = 0;

      // Evaluate cognitive load requirements
      totalChecks++;
      if (
        cognitiveAssessment.overallCognitiveLoad === 'none' ||
        cognitiveAssessment.alternatives.length > 0
      ) {
        passedChecks++;
        evidence.push({
          type: 'info' as const,
          description: 'Cognitive load assessment',
          value: `Overall load: ${cognitiveAssessment.overallCognitiveLoad}, Alternatives: ${cognitiveAssessment.alternatives.length}`,
          severity: 'info' as const,
        });
      } else {
        issues.push(
          `High cognitive load authentication without alternatives (${cognitiveAssessment.overallCognitiveLoad})`,
        );
        recommendations.push(
          'Provide authentication method that does not require cognitive function tests',
        );
      }

      // Evaluate object recognition tests
      for (const test of objectRecognition) {
        totalChecks++;

        if (test.hasObjectRecognition && !test.hasAlternative) {
          issues.push(`Object recognition test without alternative: ${test.description}`);
          recommendations.push(
            'Provide alternative authentication that does not require object recognition',
          );

          manualReviewItems.push({
            selector: test.selector,
            description: 'Object recognition in authentication',
            automatedFindings: `Found ${test.type} requiring object recognition`,
            reviewRequired:
              'Manually verify that users are not required to identify objects, images, or solve visual puzzles for authentication',
            priority: 'high' as const,
            estimatedTime: 15,
          });
        } else {
          passedChecks++;
        }
      }

      // Evaluate text transcription requirements
      for (const test of textTranscription) {
        totalChecks++;

        if (test.hasTextTranscription && !test.hasAlternative) {
          issues.push(`Text transcription test without alternative: ${test.description}`);
          recommendations.push(
            'Provide alternative authentication that does not require text transcription',
          );

          manualReviewItems.push({
            selector: test.selector,
            description: 'Text transcription in authentication',
            automatedFindings: `Found ${test.type} requiring text transcription`,
            reviewRequired:
              'Manually verify that users are not required to transcribe distorted text or solve text-based puzzles',
            priority: 'high' as const,
            estimatedTime: 12,
          });
        } else {
          passedChecks++;
        }
      }

      // Enhanced manual review items
      if (enhancedElements.length > 0) {
        manualReviewItems.push({
          selector: 'body',
          description: 'Enhanced authentication accessibility review',
          automatedFindings: `Found ${enhancedElements.length} authentication elements requiring enhanced review`,
          reviewRequired:
            'Conduct comprehensive manual testing to ensure authentication does not rely on cognitive function tests, object recognition, or personal information recall',
          priority: 'high' as const,
          estimatedTime: 30,
        });

        // Specific manual reviews for each element type
        const cognitiveTests = enhancedElements.filter((el) => el.type === 'cognitive-test');
        if (cognitiveTests.length > 0) {
          manualReviewItems.push({
            selector: cognitiveTests[0].selector,
            description: 'Cognitive function test evaluation',
            automatedFindings: `${cognitiveTests.length} potential cognitive tests detected`,
            reviewRequired:
              'Test with users who have cognitive disabilities to ensure authentication is accessible',
            priority: 'high' as const,
            estimatedTime: 25,
          });
        }

        const memoryTests = enhancedElements.filter((el) => el.hasMemoryRequirement);
        if (memoryTests.length > 0) {
          manualReviewItems.push({
            selector: memoryTests[0].selector,
            description: 'Memory requirement assessment',
            automatedFindings: `${memoryTests.length} elements requiring memory detected`,
            reviewRequired:
              'Verify that authentication alternatives exist that do not require users to remember information',
            priority: 'medium' as const,
            estimatedTime: 20,
          });
        }
      }

      // Check for enhanced alternatives
      if (enhancedAlternatives.length > 0) {
        evidence.push({
          type: 'info' as const,
          description: 'Enhanced authentication alternatives',
          value: `${enhancedAlternatives.length} enhanced alternatives found: ${enhancedAlternatives.join(', ')}`,
          severity: 'info' as const,
        });
      }

      // If no authentication found, note it
      if (enhancedElements.length === 0) {
        evidence.push({
          type: 'info' as const,
          description: 'No enhanced authentication mechanisms detected',
          value: 'Page may not require enhanced authentication',
          severity: 'info' as const,
        });
        totalChecks = 1;
        passedChecks = 1;
      }

      // Add comprehensive manual review for AAA compliance
      manualReviewItems.push({
        selector: 'form, [role="form"]',
        description: 'AAA-level authentication accessibility audit',
        automatedFindings: 'Enhanced authentication analysis completed',
        reviewRequired:
          'Perform comprehensive accessibility audit including: 1) Test with assistive technologies, 2) Verify no cognitive function tests, 3) Ensure multiple authentication options, 4) Test with users with disabilities',
        priority: 'medium' as const,
        estimatedTime: 45,
      });

      return {
        automatedScore: Math.round((passedChecks / Math.max(totalChecks, 1)) * 100),
        maxScore: 100,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
        automationRate: 0.4, // 40% automation rate
      };
    } catch (error) {
      logger.error('Error in enhanced authentication check:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        automatedScore: 0,
        maxScore: 100,
        evidence: [
          {
            type: 'error' as const,
            description: 'Error during enhanced authentication analysis',
            value: error instanceof Error ? error.message : 'Unknown error',
            severity: 'error' as const,
          },
        ],
        issues: ['Failed to analyze enhanced authentication accessibility'],
        recommendations: [
          'Review authentication mechanisms manually for AAA-level accessibility compliance',
        ],
        manualReviewItems: [
          {
            selector: 'body',
            description: 'Manual enhanced authentication review required',
            automatedFindings: 'Automated analysis failed',
            reviewRequired:
              'Manually review all authentication mechanisms for enhanced accessibility compliance',
            priority: 'high' as const,
            estimatedTime: 60,
          },
        ],
        automationRate: 0.4,
      };
    }
  }

  /**
   * Detect enhanced authentication elements
   */
  private async detectEnhancedAuthenticationElements(
    page: Page,
  ): Promise<EnhancedAuthenticationElement[]> {
    return await page.evaluate(() => {
      const elements: EnhancedAuthenticationElement[] = [];

      // Enhanced authentication selectors
      const enhancedSelectors = [
        '[class*="captcha"]',
        '[id*="captcha"]',
        '[class*="recaptcha"]',
        '[class*="puzzle"]',
        '[class*="challenge"]',
        '[class*="verification"]',
        'canvas[class*="auth"]',
        'img[class*="auth"]',
        '[class*="security-question"]',
        '[class*="personal-info"]',
        '[class*="knowledge-based"]',
        'input[placeholder*="mother"]',
        'input[placeholder*="school"]',
        'input[placeholder*="pet"]',
        'select[name*="question"]',
      ];

      enhancedSelectors.forEach((selector) => {
        const foundElements = document.querySelectorAll(selector);
        foundElements.forEach((element) => {
          const el = element as HTMLElement;

          const analysis = this.analyzeEnhancedElement(el);

          elements.push({
            selector: this.getElementSelector(el),
            type: analysis.type,
            cognitiveLoad: analysis.cognitiveLoad,
            hasObjectRecognition: analysis.hasObjectRecognition,
            hasTextTranscription: analysis.hasTextTranscription,
            hasMemoryRequirement: analysis.hasMemoryRequirement,
            hasAlternative: this.checkEnhancedAlternatives(el),
            alternativeTypes: this.getAlternativeTypes(el),
            description: analysis.description,
            severity: analysis.severity,
          });
        });
      });

      return elements;
    });
  }

  /**
   * Perform cognitive assessment
   */
  private async performCognitiveAssessment(
    page: Page,
    elements: EnhancedAuthenticationElement[],
  ): Promise<CognitiveAssessment> {
    return await page.evaluate((elements) => {
      const cognitiveTests = elements.filter(
        (el) => el.type === 'cognitive-test' || el.cognitiveLoad !== 'none',
      );

      // Determine overall cognitive load
      let overallLoad: 'none' | 'low' | 'medium' | 'high' = 'none';
      if (cognitiveTests.some((test) => test.cognitiveLoad === 'high')) {
        overallLoad = 'high';
      } else if (cognitiveTests.some((test) => test.cognitiveLoad === 'medium')) {
        overallLoad = 'medium';
      } else if (cognitiveTests.some((test) => test.cognitiveLoad === 'low')) {
        overallLoad = 'low';
      }

      // Find alternatives
      const alternatives = this.findCognitiveAlternatives();

      // Determine compliance
      let compliance: 'pass' | 'fail' | 'needs-review' = 'needs-review';
      if (overallLoad === 'none') {
        compliance = 'pass';
      } else if (alternatives.length > 0) {
        compliance = 'needs-review'; // Manual verification needed
      } else {
        compliance = 'fail';
      }

      return {
        overallCognitiveLoad: overallLoad,
        cognitiveTests,
        alternatives,
        compliance,
        recommendations: this.generateCognitiveRecommendations(overallLoad, alternatives),
      };
    }, elements);
  }

  /**
   * Check for object recognition requirements
   */
  private async checkObjectRecognition(page: Page): Promise<EnhancedAuthenticationElement[]> {
    return await page.evaluate(() => {
      const objectTests: EnhancedAuthenticationElement[] = [];

      // Look for visual recognition elements
      const visualSelectors = [
        'img[src*="captcha"]',
        'canvas[class*="captcha"]',
        '[class*="image-verification"]',
        '[class*="visual-challenge"]',
        '[class*="picture-password"]',
        '[alt*="select"]',
        '[alt*="click"]',
        '[alt*="identify"]',
      ];

      visualSelectors.forEach((selector) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element) => {
          const el = element as HTMLElement;
          objectTests.push({
            selector: this.getElementSelector(el),
            type: 'recognition-test',
            cognitiveLoad: 'high',
            hasObjectRecognition: true,
            hasTextTranscription: false,
            hasMemoryRequirement: false,
            hasAlternative: this.checkVisualAlternatives(el),
            alternativeTypes: this.getVisualAlternativeTypes(el),
            description: this.getVisualTestDescription(el),
            severity: 'error',
          });
        });
      });

      return objectTests;
    });
  }

  /**
   * Check for text transcription requirements
   */
  private async checkTextTranscription(page: Page): Promise<EnhancedAuthenticationElement[]> {
    return await page.evaluate(() => {
      const transcriptionTests: EnhancedAuthenticationElement[] = [];

      // Look for text transcription elements
      const textSelectors = [
        'img[src*="captcha"][alt*="text"]',
        '[class*="text-captcha"]',
        '[class*="distorted-text"]',
        'input[placeholder*="enter the text"]',
        'input[placeholder*="type the characters"]',
        '[class*="character-recognition"]',
      ];

      textSelectors.forEach((selector) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element) => {
          const el = element as HTMLElement;
          transcriptionTests.push({
            selector: this.getElementSelector(el),
            type: 'transcription-test',
            cognitiveLoad: 'high',
            hasObjectRecognition: false,
            hasTextTranscription: true,
            hasMemoryRequirement: false,
            hasAlternative: this.checkTextAlternatives(el),
            alternativeTypes: this.getTextAlternativeTypes(el),
            description: this.getTextTestDescription(el),
            severity: 'error',
          });
        });
      });

      return transcriptionTests;
    });
  }

  /**
   * Evaluate enhanced alternatives
   */
  private async evaluateEnhancedAlternatives(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const alternatives: string[] = [];

      // Look for enhanced alternative methods
      const enhancedAlternatives = [
        '[class*="biometric"]',
        '[class*="hardware-token"]',
        '[class*="smart-card"]',
        '[class*="certificate"]',
        '[class*="device-trust"]',
        '[class*="location-based"]',
        '[class*="behavioral"]',
        '[class*="risk-based"]',
        '[class*="adaptive"]',
        '[class*="passwordless"]',
        '[class*="magic-link"]',
        '[class*="email-verification"]',
        '[class*="sms-verification"]',
        '[class*="app-notification"]',
      ];

      enhancedAlternatives.forEach((selector) => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          alternatives.push(this.getAlternativeDescription(selector));
        }
      });

      return alternatives;
    });
  }

  /**
   * Analyze enhanced authentication element
   */
  private analyzeEnhancedElement(element: Element): EnhancedAuthenticationElement {
    const text = element.textContent?.toLowerCase() || '';

    return {
      hasObjectRecognition:
        text.includes('puzzle') || text.includes('challenge') || text.includes('captcha'),
      hasMemoryRequirement: text.includes('remember') || text.includes('security question'),
      hasTextTranscription: text.includes('recognize') || text.includes('identify'),
      hasAlternative: false,
      alternativeTypes: [],
      selector: this.getElementSelector(element),
      type: this.determineElementType(text),
      cognitiveLoad: this.assessElementCognitiveLoad(text),
      description: text.substring(0, 100),
      severity: 'warning' as const,
    };
  }

  /**
   * Get element selector for identification
   */
  private getElementSelector(element: Element): string {
    if (element.id) return `#${element.id}`;
    if (element.className) return `.${element.className.split(' ')[0]}`;
    return element.tagName.toLowerCase();
  }

  /**
   * Check for enhanced alternatives
   */
  private checkEnhancedAlternatives(element: Element): boolean {
    const form = element.closest('form') || element.parentElement;
    if (!form) return false;

    const alternatives = form.querySelectorAll(
      [
        '[data-enhanced-alternative]',
        '.biometric-auth',
        '.hardware-token',
        '.oauth-login',
        '[aria-label*="biometric"]',
        '[aria-label*="token"]',
      ].join(','),
    );

    return alternatives.length > 0;
  }

  /**
   * Get alternative types available
   */
  private getAlternativeTypes(element: Element): string[] {
    const form = element.closest('form') || element.parentElement;
    if (!form) return [];

    const types: string[] = [];

    if (form.querySelector('.biometric-auth, [data-biometric]')) types.push('Biometric');
    if (form.querySelector('.hardware-token, [data-token]')) types.push('Hardware Token');
    if (form.querySelector('.oauth-login, [data-oauth]')) types.push('OAuth');
    if (form.querySelector('.sso-login, [data-sso]')) types.push('SSO');

    return types;
  }

  /**
   * Find cognitive alternatives
   */
  private findCognitiveAlternatives(): string[] {
    // This would typically scan the entire page for alternative auth methods
    return [
      'Biometric authentication available',
      'Hardware token support detected',
      'OAuth integration present',
    ];
  }

  /**
   * Generate cognitive recommendations
   */
  private generateCognitiveRecommendations(
    cognitiveLoad: string,
    alternatives: string[],
  ): string[] {
    const recommendations: string[] = [];

    if (cognitiveLoad === 'high') {
      recommendations.push('Eliminate cognitive function tests for AAA compliance');
      recommendations.push('Implement biometric authentication as primary method');
    }

    if (alternatives.length === 0) {
      recommendations.push(
        'Add alternative authentication methods that do not require cognitive function tests',
      );
    }

    recommendations.push('Ensure all authentication methods meet AAA accessibility standards');

    return recommendations;
  }

  /**
   * Check visual alternatives
   */
  private checkVisualAlternatives(element: Element): boolean {
    const form = element.closest('form') || element.parentElement;
    if (!form) return false;

    const alternatives = form.querySelectorAll(
      ['[data-audio-alternative]', '.audio-captcha', '.voice-auth', '[aria-label*="audio"]'].join(
        ',',
      ),
    );

    return alternatives.length > 0;
  }

  /**
   * Get visual alternative types
   */
  private getVisualAlternativeTypes(element: Element): string[] {
    const form = element.closest('form') || element.parentElement;
    if (!form) return [];

    const types: string[] = [];

    if (form.querySelector('.audio-captcha, [data-audio]')) types.push('Audio CAPTCHA');
    if (form.querySelector('.voice-auth, [data-voice]')) types.push('Voice Authentication');
    if (form.querySelector('[data-text-alternative]')) types.push('Text Alternative');

    return types;
  }

  /**
   * Get visual test description
   */
  private getVisualTestDescription(element: Element): string {
    const text = element.textContent?.substring(0, 100) || '';
    const type = this.determineVisualTestType(element);
    return `${type}: ${text}`;
  }

  /**
   * Check text alternatives
   */
  private checkTextAlternatives(element: Element): boolean {
    const form = element.closest('form') || element.parentElement;
    if (!form) return false;

    const alternatives = form.querySelectorAll(
      [
        '[data-visual-alternative]',
        '.visual-captcha',
        '.image-auth',
        '[aria-label*="visual"]',
      ].join(','),
    );

    return alternatives.length > 0;
  }

  /**
   * Get text alternative types
   */
  private getTextAlternativeTypes(element: Element): string[] {
    const form = element.closest('form') || element.parentElement;
    if (!form) return [];

    const types: string[] = [];

    if (form.querySelector('.visual-captcha, [data-visual]')) types.push('Visual CAPTCHA');
    if (form.querySelector('.image-auth, [data-image]')) types.push('Image Authentication');
    if (form.querySelector('[data-pattern-alternative]')) types.push('Pattern Alternative');

    return types;
  }

  /**
   * Get text test description
   */
  private getTextTestDescription(element: Element): string {
    const text = element.textContent?.substring(0, 100) || '';
    const type = this.determineTextTestType(element);
    return `${type}: ${text}`;
  }

  /**
   * Get alternative description
   */
  private getAlternativeDescription(selector: string): string {
    return `Alternative authentication method available: ${selector}`;
  }

  /**
   * Helper methods for element analysis
   */
  private determineElementType(
    text: string,
  ): 'cognitive-test' | 'memory-test' | 'recognition-test' | 'transcription-test' | 'other' {
    if (text.includes('captcha') || text.includes('puzzle')) return 'cognitive-test';
    if (text.includes('remember') || text.includes('security question')) return 'memory-test';
    if (text.includes('recognize') || text.includes('identify')) return 'recognition-test';
    if (text.includes('type') || text.includes('enter')) return 'transcription-test';
    return 'other';
  }

  private assessElementCognitiveLoad(text: string): 'none' | 'low' | 'medium' | 'high' {
    if (text.includes('captcha') || text.includes('complex')) return 'high';
    if (text.includes('remember') || text.includes('security')) return 'medium';
    if (text.includes('simple') || text.includes('basic')) return 'low';
    return 'none';
  }

  private determineVisualTestType(element: Element): string {
    const text = element.textContent?.toLowerCase() || '';

    if (text.includes('captcha')) return 'Visual CAPTCHA';
    if (text.includes('image')) return 'Image Recognition';
    if (text.includes('pattern')) return 'Pattern Recognition';
    return 'Visual Test';
  }

  private determineTextTestType(element: Element): string {
    const text = element.textContent?.toLowerCase() || '';

    if (text.includes('transcribe')) return 'Text Transcription';
    if (text.includes('type')) return 'Text Input';
    if (text.includes('read')) return 'Text Reading';
    return 'Text Test';
  }
}

// Helper function to import logger
import logger from '../../../utils/logger';
