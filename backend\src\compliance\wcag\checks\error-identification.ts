/**
 * WCAG Rule 8: Error Identification - 3.3.1
 * 90% Automated - Manual review for error message clarity
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import FormAccessibilityAnalyzer, {
  FormAccessibilityReport,
  FormAnalysis,
  FormFieldAnalysis,
} from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface ErrorIdentificationConfig extends EnhancedCheckConfig, ManualReviewConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableErrorPatternRecognition?: boolean;
  enableMessageQualityAnalysis?: boolean;
}

export class ErrorIdentificationCheck {
  private checkTemplate = new ManualReviewTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();

  /**
   * Perform error identification check - 90% automated with enhanced evidence
   */
  async performCheck(config: ErrorIdentificationConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorIdentificationConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableSemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableErrorPatternRecognition: true,
      enableMessageQualityAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-008',
      'Error Identification',
      'understandable',
      0.1,
      'A',
      enhancedConfig,
      this.executeErrorIdentificationCheck.bind(this),
      true, // Requires browser
      true, // Manual review for error message clarity
    );

    // Enhanced evidence standardization with form validation specifics
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-008',
        ruleName: 'Error Identification',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'form-validation-analysis',
          manualReviewRequired: (result.manualReviewItems?.length ?? 0) > 0,
          formAnalysis: true,
          errorMessageValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8, // High threshold for form accessibility
        maxEvidenceItems: 40,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive error identification analysis with enhanced FormAccessibilityAnalyzer
   */
  private async executeErrorIdentificationCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Enhanced form accessibility analysis
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeErrorHandling: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze form validation using enhanced analyzer
    const formAnalysis = await this.analyzeFormValidationEnhanced(page, formAccessibilityReport);

    // Analyze existing error messages with quality assessment
    const errorMessageAnalysis = await this.analyzeErrorMessagesEnhanced(page);

    // Test form submission for error handling with enhanced detection
    const submissionAnalysis = await this.testFormSubmissionEnhanced(page);

    // Combine all analyses
    const allAnalyses = [formAnalysis, errorMessageAnalysis, submissionAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Error identification analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass automated tests, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      score: automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze form validation attributes and patterns
   */
  private async analyzeFormValidation(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      const formInputs = Array.from(
        document.querySelectorAll('input:not([type="hidden"]), select, textarea'),
      );
      let totalChecks = 0;
      let passedChecks = 0;

      if (formInputs.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      formInputs.forEach((input, index) => {
        const element = input as HTMLInputElement;
        const selector = generateSelector(element, index);

        // Check for required field indication
        totalChecks++;
        const isRequired =
          element.hasAttribute('required') || element.getAttribute('aria-required') === 'true';

        if (isRequired) {
          // Check if required fields have proper indication
          const hasRequiredIndicator =
            element.getAttribute('aria-label')?.includes('required') ||
            element.getAttribute('aria-describedby') ||
            element.placeholder?.includes('required') ||
            element.title?.includes('required');

          const parentLabel =
            element.closest('label') || document.querySelector(`label[for="${element.id}"]`);
          const labelHasIndicator =
            parentLabel?.textContent?.includes('*') ||
            parentLabel?.textContent?.includes('required');

          if (hasRequiredIndicator || labelHasIndicator) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Required field has proper indication',
              value: 'Field is marked as required with appropriate indicator',
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Required field lacks clear indication: ${selector}`);
            recommendations.push(
              `Add visual and programmatic indication for required field: ${selector}`,
            );
          }
        } else {
          passedChecks++; // Non-required fields pass this check
        }

        // Check for validation patterns
        totalChecks++;
        const hasPattern =
          element.pattern ||
          element.type === 'email' ||
          element.type === 'url' ||
          element.type === 'tel';

        if (hasPattern) {
          // Check if pattern has description
          const hasPatternDescription =
            element.getAttribute('aria-describedby') || element.title || element.placeholder;

          if (hasPatternDescription) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Input pattern has description',
              value: `Pattern validation with description provided`,
              selector,
              severity: 'info',
            });
          } else {
            manualReviewItems.push({
              selector,
              description: 'Input pattern description verification needed',
              automatedFindings: 'Field has validation pattern but no clear description',
              reviewRequired: 'Verify that users understand the required input format',
              priority: 'medium',
              estimatedTime: 2,
            });
          }
        } else {
          passedChecks++; // Fields without patterns pass this check
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze existing error messages on the page
   */
  private async analyzeErrorMessages(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      // Look for common error message patterns
      const errorSelectors = [
        '.error',
        '.error-message',
        '.field-error',
        '.validation-error',
        '[role="alert"]',
        '[aria-live="polite"]',
        '[aria-live="assertive"]',
        '.invalid',
        '.has-error',
        '.form-error',
      ];

      let errorElements: Element[] = [];
      errorSelectors.forEach((selector) => {
        try {
          const elements = Array.from(document.querySelectorAll(selector));
          errorElements = errorElements.concat(elements);
        } catch (e) {
          // Ignore invalid selectors
        }
      });

      // Remove duplicates
      errorElements = errorElements.filter(
        (element, index, self) => self.indexOf(element) === index,
      );

      const totalChecks = errorElements.length;
      let passedChecks = 0;

      if (errorElements.length === 0) {
        // No error messages found - this could be good or bad
        evidence.push({
          type: 'text',
          description: 'No error messages found on page',
          value: 'Page may not have forms or errors are not currently displayed',
          severity: 'info',
        });
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      errorElements.forEach((element, index) => {
        const selector = element.id ? `#${element.id}` : `.error:nth-of-type(${index + 1})`;
        const errorText = element.textContent?.trim() || '';

        // Check if error message is descriptive
        if (errorText.length > 10 && !errorText.toLowerCase().includes('error')) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Error message is descriptive',
            value: `Message: "${errorText.substring(0, 100)}"`,
            selector,
            severity: 'info',
          });
        } else if (errorText.length <= 10) {
          issues.push(`Error message too short: ${selector}`);
          recommendations.push(`Provide more descriptive error message for ${selector}`);
        } else {
          manualReviewItems.push({
            selector,
            description: 'Error message clarity verification needed',
            automatedFindings: `Error message: "${errorText}"`,
            reviewRequired:
              'Verify that error message clearly explains the problem and how to fix it',
            priority: 'high',
            estimatedTime: 2,
          });
        }

        // Check if error is associated with form field
        const ariaDescribedby = element.id;
        if (ariaDescribedby) {
          const associatedField = document.querySelector(
            `[aria-describedby*="${ariaDescribedby}"]`,
          );
          if (associatedField) {
            evidence.push({
              type: 'text',
              description: 'Error message is associated with form field',
              value: 'Proper aria-describedby association found',
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Error message not associated with form field: ${selector}`);
            recommendations.push(`Associate error message with form field using aria-describedby`);
          }
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Test form submission for error handling
   */
  private async testFormSubmission(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    const evidence: Array<{
      type:
        | 'text'
        | 'image'
        | 'code'
        | 'measurement'
        | 'interaction'
        | 'info'
        | 'warning'
        | 'error';
      description: string;
      value: string;
      selector?: string;
      severity?: 'info' | 'warning' | 'error' | 'critical';
    }> = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: Array<{
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
    }> = [];
    let totalChecks = 0;
    let passedChecks = 0;

    try {
      // Find forms on the page
      const forms = await page.$$('form');

      if (forms.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      for (let i = 0; i < Math.min(forms.length, 2); i++) {
        // Test max 2 forms to avoid excessive testing
        totalChecks++;

        try {
          // Try to submit form without filling required fields
          const formSelector = `form:nth-of-type(${i + 1})`;

          // Check if form has required fields
          const hasRequiredFields = await page.evaluate((selector) => {
            const form = document.querySelector(selector) as HTMLFormElement;
            if (!form) return false;

            const requiredInputs = form.querySelectorAll(
              'input[required], select[required], textarea[required]',
            );
            return requiredInputs.length > 0;
          }, formSelector);

          if (hasRequiredFields) {
            // This requires manual testing as automated submission might have side effects
            manualReviewItems.push({
              selector: formSelector,
              description: 'Form error handling verification needed',
              automatedFindings: 'Form has required fields that need validation testing',
              reviewRequired:
                'Test form submission with missing/invalid data to verify error messages appear and are helpful',
              priority: 'high',
              estimatedTime: 5,
            });
          } else {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Form structure analyzed',
              value: 'Form does not have required fields or validation needs manual testing',
              selector: formSelector,
              severity: 'info',
            });
          }
        } catch (error) {
          manualReviewItems.push({
            selector: `form:nth-of-type(${i + 1})`,
            description: 'Form testing failed - manual verification needed',
            automatedFindings: 'Automated form testing encountered errors',
            reviewRequired:
              'Manually test form submission with invalid data to verify error handling',
            priority: 'high',
            estimatedTime: 5,
          });
        }
      }
    } catch (error) {
      issues.push('Error testing form submission');
      recommendations.push('Manual form submission testing required');
    }

    return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
  }

  /**
   * Enhanced form validation analysis using FormAccessibilityAnalyzer
   */
  private async analyzeFormValidationEnhanced(
    page: Page,
    formAccessibilityReport: FormAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    const totalChecks = formAccessibilityReport.totalForms;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form: FormAnalysis, index: number) => {
      // Check error handling capabilities
      if (form.errorHandling.hasErrorSummary && form.errorHandling.errorsLinkedToFields) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Form ${index + 1} has proper error identification`,
          value: `${form.selector} - errorSummary: ${form.errorHandling.hasErrorSummary}, linkedErrors: ${form.errorHandling.errorsLinkedToFields}`,
          selector: form.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Form ${index + 1} lacks proper error identification`);
        evidence.push({
          type: 'code',
          description: `Form ${index + 1} requires enhanced error identification`,
          value: `${form.selector} - missing error summary or linked error messages`,
          selector: form.selector,
          severity: 'error',
        });
        recommendations.push(
          `Add error summary and link error messages to fields in form ${index + 1}`,
        );
      }

      // Check individual field error handling
      form.fields.forEach((field: FormFieldAnalysis, fieldIndex: number) => {
        if (!field.validation.hasAccessibleErrors && field.validation.isRequired) {
          issues.push(
            `Required field ${fieldIndex + 1} in form ${index + 1} lacks error identification`,
          );
          evidence.push({
            type: 'code',
            description: `Required field lacks error identification`,
            value: `${field.selector} - required field without accessible error messages`,
            selector: field.selector,
            severity: 'error',
          });
          recommendations.push(`Add accessible error messages to required field ${fieldIndex + 1}`);
        }
      });

      // Add manual review for complex error scenarios
      if (form.fields.length > 5) {
        manualReviewItems.push({
          selector: form.selector,
          description: `Manual review required for complex form ${index + 1} error handling`,
          automatedFindings: `Form has ${form.fields.length} fields with error handling analysis`,
          reviewRequired:
            'Test form with various error scenarios to ensure all errors are properly identified and communicated',
          priority: 'medium',
          estimatedTime: 8,
          type: 'manual',
          element: form.selector,
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
    };
  }

  /**
   * Enhanced error message analysis with quality assessment
   */
  private async analyzeErrorMessagesEnhanced(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Enhanced error message detection
    const errorMessages = await page.$$eval(
      '[role="alert"], .error, .error-message, .field-error, [aria-live="polite"], [aria-live="assertive"], .invalid-feedback, .help-block.error',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.trim() || '';
          const isVisible = (element as HTMLElement).offsetParent !== null;
          const hasAriaLive = element.hasAttribute('aria-live');
          const role = element.getAttribute('role');
          const associatedField =
            element.getAttribute('aria-describedby') ||
            element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          return {
            index,
            text,
            isVisible,
            hasAriaLive,
            role,
            hasAssociatedField: !!associatedField,
            selector: `error-message-${index}`,
            isEmpty: text.length === 0,
            isGeneric: text.toLowerCase().includes('error') && text.length < 20,
          };
        });
      },
    );

    const totalChecks = errorMessages.length;
    let passedChecks = 0;

    errorMessages.forEach((errorMsg, index) => {
      let fieldPassed = true;

      // Check if error message is visible and has content
      if (!errorMsg.isVisible || errorMsg.isEmpty) {
        fieldPassed = false;
        issues.push(`Error message ${index + 1} is not visible or empty`);
        evidence.push({
          type: 'code',
          description: `Error message ${index + 1} visibility issue`,
          value: `visible: ${errorMsg.isVisible}, hasContent: ${!errorMsg.isEmpty}`,
          severity: 'error',
        });
        recommendations.push(
          `Ensure error message ${index + 1} is visible and has meaningful content`,
        );
      }

      // Check if error message is properly associated with form field
      if (!errorMsg.hasAssociatedField) {
        fieldPassed = false;
        issues.push(`Error message ${index + 1} is not associated with a form field`);
        evidence.push({
          type: 'code',
          description: `Error message ${index + 1} lacks field association`,
          value: `hasAssociatedField: ${errorMsg.hasAssociatedField}`,
          severity: 'error',
        });
        recommendations.push(
          `Associate error message ${index + 1} with its form field using aria-describedby`,
        );
      }

      // Check for generic error messages
      if (errorMsg.isGeneric) {
        issues.push(`Error message ${index + 1} is too generic`);
        evidence.push({
          type: 'code',
          description: `Error message ${index + 1} needs more specific content`,
          value: `message: "${errorMsg.text}"`,
          severity: 'warning',
        });
        recommendations.push(`Make error message ${index + 1} more specific and helpful`);

        // Add manual review for message quality
        manualReviewItems.push({
          selector: errorMsg.selector,
          description: `Manual review required for error message ${index + 1} quality`,
          automatedFindings: `Generic error message detected: "${errorMsg.text}"`,
          reviewRequired: 'Review error message for clarity, specificity, and helpfulness to users',
          priority: 'medium',
          estimatedTime: 3,
          type: 'manual',
          element: errorMsg.selector,
        });
      }

      if (fieldPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Error message ${index + 1} is properly implemented`,
          value: `"${errorMsg.text}" - visible, associated, and meaningful`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
    };
  }

  /**
   * Enhanced form submission testing with error detection
   */
  private async testFormSubmissionEnhanced(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Find forms that can be tested
      const testableForms = await page.$$eval('form', (forms) => {
        return forms
          .map((form, index) => {
            const submitButton = form.querySelector(
              'input[type="submit"], button[type="submit"], button:not([type])',
            );
            const requiredFields = form.querySelectorAll('[required], [aria-required="true"]');

            return {
              index,
              hasSubmitButton: !!submitButton,
              requiredFieldCount: requiredFields.length,
              canTest: !!submitButton && requiredFields.length > 0,
              selector: `form:nth-of-type(${index + 1})`,
            };
          })
          .filter((form) => form.canTest);
      });

      const totalChecks = testableForms.length;
      let passedChecks = 0;

      for (const form of testableForms) {
        try {
          // Test form submission with empty required fields
          const errorDetected = await page.evaluate((formSelector) => {
            const formElement = document.querySelector(formSelector) as HTMLFormElement;
            if (!formElement) return false;

            // Clear required fields
            const requiredFields = formElement.querySelectorAll(
              '[required], [aria-required="true"]',
            );
            requiredFields.forEach((element) => {
              const field = element as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
              if ('type' in field && (field.type === 'checkbox' || field.type === 'radio')) {
                (field as HTMLInputElement).checked = false;
              } else {
                field.value = '';
              }
            });

            // Try to submit
            const submitButton = formElement.querySelector(
              'input[type="submit"], button[type="submit"], button:not([type])',
            ) as HTMLElement;
            if (submitButton) {
              submitButton.click();
            }

            // Check for error messages after a brief delay
            return new Promise((resolve) => {
              setTimeout(() => {
                const errorElements = document.querySelectorAll(
                  '[role="alert"], .error, .error-message, .field-error',
                );
                const hasVisibleErrors = Array.from(errorElements).some(
                  (el) => (el as HTMLElement).offsetParent !== null,
                );
                resolve(hasVisibleErrors);
              }, 500);
            });
          }, form.selector);

          if (errorDetected) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Form ${form.index + 1} properly displays errors on invalid submission`,
              value: `${form.selector} - error detection working`,
              selector: form.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Form ${form.index + 1} does not display errors on invalid submission`);
            evidence.push({
              type: 'code',
              description: `Form ${form.index + 1} lacks error display on submission`,
              value: `${form.selector} - no errors shown for invalid submission`,
              selector: form.selector,
              severity: 'error',
            });
            recommendations.push(`Add error display functionality to form ${form.index + 1}`);
          }
        } catch (testError) {
          // Add manual review for forms that couldn't be automatically tested
          manualReviewItems.push({
            selector: form.selector,
            description: `Manual testing required for form ${form.index + 1}`,
            automatedFindings: 'Automated form testing encountered errors',
            reviewRequired:
              'Manually test form submission with invalid data to verify error handling',
            priority: 'high',
            estimatedTime: 5,
            type: 'manual',
            element: form.selector,
          });
        }
      }

      if (totalChecks === 0) {
        evidence.push({
          type: 'text',
          description: 'No testable forms found on page',
          value: 'No forms with required fields and submit buttons detected',
          severity: 'info',
        });
        recommendations.push('Manual form submission testing required');
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error during form submission testing',
        value: `testing-error - ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Form submission testing failed'],
        recommendations: ['Manual form submission testing required'],
        manualReviewItems,
      };
    }
  }
}
