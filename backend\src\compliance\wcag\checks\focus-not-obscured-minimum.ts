/**
 * WCAG Rule 10: Focus Not Obscured (Minimum) - 2.4.11
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { FocusTracker } from '../utils/focus-tracker';
import { AdvancedFocusTracker } from '../utils/advanced-focus-tracker';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface FocusNotObscuredConfig extends EnhancedCheckConfig {
  enableAdvancedFocusTracking?: boolean;
  enableLayoutAnalysis?: boolean;
  enableOverlayDetection?: boolean;
  enableFrameworkOptimization?: boolean;
  enableDynamicContentAnalysis?: boolean;
  enableThirdPartyLibraries?: boolean;
  enableWideGamutAnalysis?: boolean;
  enableAdvancedColorSpaces?: boolean;
  enableKeyboardTrapping?: boolean;
}

export class FocusNotObscuredMinimumCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private layoutAnalyzer = new LayoutAnalyzer();
  private focusTracker = new FocusTracker();
  private advancedFocusTracker = AdvancedFocusTracker.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getWideGamutInstance();

  /**
   * Perform focus not obscured minimum check - 100% automated with enhanced evidence
   */
  async performCheck(config: FocusNotObscuredConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: FocusNotObscuredConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableAdvancedFocusTracking: true,
      enableLayoutAnalysis: true,
      enableOverlayDetection: true,
      enableFrameworkOptimization: true,
      enableDynamicContentAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-010',
      'Focus Not Obscured (Minimum)',
      'operable',
      0.06,
      'AA',
      enhancedConfig,
      this.executeFocusNotObscuredCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with focus obstruction analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-010',
        ruleName: 'Focus Not Obscured (Minimum)',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 1.0,
          checkType: 'focus-obstruction-analysis',
          layoutAnalysis: true,
          focusTracking: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 30,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute focus obstruction analysis
   */
  private async executeFocusNotObscuredCheck(page: Page, _config: CheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    let totalElements = 0;
    let passedElements = 0;

    // Enhanced focus obstruction analysis using AdvancedFocusTracker
    try {
      const focusObstructionAnalysis =
        await this.advancedFocusTracker.analyzeFocusObstruction(page);

      // Add enhanced evidence from advanced focus obstruction analysis
      evidence.push({
        type: 'info',
        description: 'Advanced focus obstruction analysis with overlay detection',
        element: 'focus-obstructions',
        value: JSON.stringify({
          overallScore: focusObstructionAnalysis.overallScore,
          criticalIssues: focusObstructionAnalysis.criticalIssues,
          recommendations: focusObstructionAnalysis.recommendations,
          performanceMetrics: focusObstructionAnalysis.performanceMetrics,
        }),
        severity: focusObstructionAnalysis.criticalIssues.length > 0 ? 'error' : 'info',
      });

      // Collect issues and recommendations from advanced analysis
      if (focusObstructionAnalysis.criticalIssues.length > 0) {
        issues.push(...focusObstructionAnalysis.criticalIssues);
        recommendations.push(...focusObstructionAnalysis.recommendations);
      }
    } catch (error) {
      console.warn(
        'Advanced focus obstruction analysis failed, falling back to basic analysis:',
        error,
      );
    }

    try {
      // Get all focusable elements - Basic fallback analysis
      const focusableElements = await FocusTracker.getFocusableElements(page);

      if (!focusableElements || focusableElements.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No focusable elements found on page',
          value:
            'Page may not have interactive content or elements are not properly marked as focusable',
          severity: 'warning',
        });

        return {
          score: 100, // No focusable elements means no focus obstruction issues
          maxScore: 100,
          evidence,
          issues: [],
          recommendations: ['Ensure interactive elements are properly focusable'],
        };
      }

      // Test each focusable element for obstruction
      for (const element of focusableElements) {
        try {
          totalElements++;

          // Focus the element with error handling
          try {
            await page.focus(element.selector);
          } catch (focusError) {
            issues.push(
              `Unable to focus element ${element.selector}: ${focusError instanceof Error ? focusError.message : 'Unknown focus error'}`,
            );
            continue;
          }

          // Check if element is obscured with error handling
          let obscurationResult;
          try {
            obscurationResult = await LayoutAnalyzer.checkElementObscured(page, element.selector);
          } catch (obscurationError) {
            issues.push(
              `Unable to check obscuration for ${element.selector}: ${obscurationError instanceof Error ? obscurationError.message : 'Unknown obscuration error'}`,
            );
            continue;
          }

          if (!obscurationResult.isObscured) {
            passedElements++;

            evidence.push({
              type: 'measurement',
              description: 'Focused element is not obscured by other content',
              value: 'Element remains fully visible when focused',
              selector: element.selector,
              severity: 'info',
            });
          } else {
            issues.push(
              `Focused element ${element.selector} is obscured by: ${obscurationResult.obscuringElements.join(', ')}`,
            );

            evidence.push({
              type: 'measurement',
              description: 'Focused element is obscured by other content',
              value: `Obscured by: ${obscurationResult.obscuringElements.join(', ')}`,
              selector: element.selector,
              severity: 'error',
            });

            recommendations.push(
              `Ensure ${element.selector} is not hidden by fixed/sticky elements when focused`,
            );
          }
        } catch (elementError) {
          issues.push(
            `Error processing element ${element.selector}: ${elementError instanceof Error ? elementError.message : 'Unknown element error'}`,
          );
        }
      }
    } catch (error) {
      // Top-level error handling
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error in focus obstruction check';
      issues.push(`Focus obstruction check failed: ${errorMessage}`);

      evidence.push({
        type: 'text',
        description: 'Focus obstruction check encountered an error',
        value: errorMessage,
        severity: 'error',
      });

      return {
        score: 0,
        maxScore: 100,
        evidence,
        issues,
        recommendations: [
          'Manual review required due to technical error',
          'Check browser console for detailed error information',
        ],
      };
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Focus obstruction analysis summary',
      value: `${passedElements}/${totalElements} focused elements remain unobscured`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift(
        'Review fixed and sticky positioned elements that may obscure focused content',
      );
      recommendations.push('Consider using scroll-padding or scroll-margin CSS properties');
      recommendations.push('Ensure focused elements are scrolled into view when needed');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
