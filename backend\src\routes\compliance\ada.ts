import * as express from 'express';
import { Request, Response, Router } from 'express';
import { checkImageAltText } from '../../compliance/ada/image-alt-text-check';
import { AdaCheckResult } from '../../compliance/ada/types';
import { z } from 'zod';

const router: Router = express.Router();

// Zod schema for request body validation
const AdaScanRequestSchema = z.object({
  url: z.string().url({ message: 'Invalid URL format. Please include http:// or https://' }),
});

/**
 * @openapi
 * /compliance/ada/image-alt-text:
 *   post:
 *     summary: Perform an ADA image alt text check on a given URL.
 *     tags: [ADA Compliance]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - url
 *             properties:
 *               url:
 *                 type: string
 *                 format: url
 *                 description: The URL to scan for image alt text compliance.
 *                 example: https://example.com
 *     responses:
 *       200:
 *         description: ADA image alt text check completed successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AdaCheckResult' // Assuming you'll define this in OpenAPI spec
 *       400:
 *         description: Invalid request (e.g., missing URL, invalid URL format).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Internal server error during the scan.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
router.post('/image-alt-text', async (req: Request, res: Response) => {
  try {
    const validationResult = AdaScanRequestSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Invalid request body.',
        errors: validationResult.error.format()._errors, // Top-level errors
      });
    }

    const { url } = validationResult.data;
    const result: AdaCheckResult = await checkImageAltText(url);
    return res.status(200).json(result);
  } catch (error: unknown) {
    // Log the error for server-side inspection
    console.error(`Error during ADA image alt text scan for URL: ${req.body?.url}`, error);

    const message = error instanceof Error ? error.message : 'An unexpected error occurred.';
    // Avoid sending detailed internal error messages to the client unless necessary
    return res
      .status(500)
      .json({ message: `Failed to perform ADA image alt text check: ${message}` });
  }
});

export default router;
