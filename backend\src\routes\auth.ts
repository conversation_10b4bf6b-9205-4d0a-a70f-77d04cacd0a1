import * as express from 'express';
import { Router, Request, Response } from 'express';
import { User } from '../types'; // Assuming User type is defined in src/types
import { keycloak } from '../lib/keycloak';
import db from '../lib/db';

const router: Router = express.Router();

// Define interfaces for Keycloak token and authenticated request
interface KeycloakTokenContent {
  sub: string;
  email?: string;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  // Add other fields you expect from your Keycloak token
}

interface AuthenticatedRequest extends Request {
  kauth?: {
    grant?: {
      access_token?: {
        content: KeycloakTokenContent;
      };
    };
  };
}

// GET /api/v1/auth/me - Get current user's profile, create if not exists
router.get('/me', keycloak.protect(), async (req: AuthenticatedRequest, res: Response) => {
  const tokenContent = req.kauth?.grant?.access_token?.content;

  if (!tokenContent || !tokenContent.sub || !tokenContent.email) {
    return res.status(401).json({ message: 'User Keycloak ID or email not found in token.' });
  }

  const keycloakId = tokenContent.sub;
  const email = tokenContent.email;
  // const preferredUsername = tokenContent.preferred_username;
  // const givenName = tokenContent.given_name;
  // const familyName = tokenContent.family_name;

  try {
    let user = await db<User>('users').where({ keycloak_id: keycloakId }).first();

    if (!user) {
      console.log(`User with keycloak_id ${keycloakId} not found. Creating new user.`);
      // User does not exist, create them
      const [newUser] = await db<User>('users')
        .insert({
          keycloak_id: keycloakId,
          email: email,
          // You could add other fields here if available and needed, e.g., from tokenContent
          // created_at and updated_at will be handled by database defaults
        })
        .returning('*');
      user = newUser;
      console.log('New user created:', user);
    } else {
      // Optionally, update user details if they have changed in Keycloak (e.g., email)
      // For now, we'll just return the existing user.
      // if (user.email !== email) {
      //   await db('users').where({ id: user.id }).update({ email: email, updated_at: new Date() });
      //   user.email = email;
      // }
      console.log('User found:', user);
    }

    // Return essential user information (do not return sensitive data like hashed passwords if any)
    // Our users table doesn't store passwords as Keycloak handles that.
    res.status(200).json({
      id: user.id, // Our internal DB id
      keycloakId: user.keycloak_id,
      email: user.email,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
    });
  } catch (error) {
    console.error('Error in /auth/me route:', error);
    res.status(500).json({
      message: 'Failed to process user information.',
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

// Placeholder for future auth-related routes (e.g., get user profile, logout)
// Keycloak handles core login/registration

// Placeholder for future auth-related routes (e.g., get user profile, logout)
// Keycloak handles core login/registration

export default router;
